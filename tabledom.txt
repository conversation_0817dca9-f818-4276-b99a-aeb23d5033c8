<div class="box-body" style="overflow-x: auto;overflow-y: hidden;padding:0;">
        
    <table class="table table-hover " style="min-width: 1600px;table-layout: auto;">
        
        <tbody>
        
            <tr>
                
                    <th style="text-align: center;">
                        <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-select-all" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                    </th>
                
                
                    
                        
                            <th>
                        
                        ID
                        
                            <a class="fa fa-fw fa-sort" id="sort-id" href="?__sort=id&amp;__sort_type=desc&amp;__go_admin_no_animation_=true&amp;__pageSize=10&amp;__go_admin_no_animation_=true&amp;__pageSize=10"></a>
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 150px">
                        
                        订单号
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 110px">
                        
                        数字人标识
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 80px">
                        
                        Luck ID
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        原图
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        打印照片
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        门店
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        设备号
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        重新制作
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        手机号
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        付款方式
                        
                        </th>
                    
                
                    
                        
                            <th>
                        
                        付款金额(元)
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 80px">
                        
                        支付状态
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 150px">
                        
                        支付时间
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 110px">
                        
                        支付单号
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 80px">
                        
                        来源
                        
                        </th>
                    
                
                    
                        
                            <th style="width: 120px">
                        
                        创建时间
                        
                        </th>
                    
                
                
                    <th style="text-align: center;" class="last_th_td_ele">操作</th>
                
            </tr>
        


        
        
        
        
        
        
        
        
        
        
        
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1065" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1065</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>a3b56…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: a3b560cab51c44ed';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('a3b560cab51c44ed')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(a3b560cab51c44ed)" href="/admin/info/sub_orders?order_id=1065">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#fff3e0;color:#f57c00;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #ffcc02;">
					<i class="fa fa-clock-o" style="margin-right:3px;"></i>待支付
				</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>E421E…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: E421E04E471585BFC1C6A1887422BFED';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('E421E04E471585BFC1C6A1887422BFED')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td><nil></nil></td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250804%2FTEMPC8D7F104638676DA65BD8219027B%2F3KJSP99PVG9WLA0J.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=ZvwTs8gvFoNtHeGkok4jqo8Kg1I%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_acdb10aa-7963-4068-9a70-3a3d546940c7" style="cursor: zoom-in;">
        <div id="img_acdb10aa-7963-4068-9a70-3a3d546940c7" class="modal fade acdb10aa-7963-4068-9a70-3a3d546940c7" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog acdb10aa-7963-4068-9a70-3a3d546940c7">
                <div class="modal-content acdb10aa-7963-4068-9a70-3a3d546940c7">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250804%2FTEMPC8D7F104638676DA65BD8219027B%2F3KJSP99PVG9WLA0J.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=ZvwTs8gvFoNtHeGkok4jqo8Kg1I%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.acdb10aa-7963-4068-9a70-3a3d546940c7");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.acdb10aa-7963-4068-9a70-3a3d546940c7').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250804%2FTEMPC8D7F104638676DA65BD8219027B%2FXUZHWEFOZI39LRCS.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=CioUykjuyYVA8C51qYir8SIySto%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_d9e84145-fe16-4689-a83c-294c5c4e854b" style="cursor: zoom-in;">
        <div id="img_d9e84145-fe16-4689-a83c-294c5c4e854b" class="modal fade d9e84145-fe16-4689-a83c-294c5c4e854b" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog d9e84145-fe16-4689-a83c-294c5c4e854b">
                <div class="modal-content d9e84145-fe16-4689-a83c-294c5c4e854b">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250804%2FTEMPC8D7F104638676DA65BD8219027B%2FXUZHWEFOZI39LRCS.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=CioUykjuyYVA8C51qYir8SIySto%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.d9e84145-fe16-4689-a83c-294c5c4e854b");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.d9e84145-fe16-4689-a83c-294c5c4e854b').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1065" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#909399;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;"><circle cx="7" cy="7" r="6" fill="#909399"></circle></svg>未支付</span></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-04 10:53:59</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1065&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1064" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1064</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>22d67…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 22d678e5089b452e';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('22d678e5089b452e')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(22d678e5089b452e)" href="/admin/info/sub_orders?order_id=1064">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>E243F…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: E243F1B1E7E08C8FBDF2D60E09817D67';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('E243F1B1E7E08C8FBDF2D60E09817D67')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11179</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP63C3D58DF4E1380A388FBA630C99%2FP5YA0Z1W2K40V4YI.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=oRZplx377bX4msFCLiv7wunzHvI%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_e9486521-e667-4dc6-a663-57007d3c4944" style="cursor: zoom-in;">
        <div id="img_e9486521-e667-4dc6-a663-57007d3c4944" class="modal fade e9486521-e667-4dc6-a663-57007d3c4944" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog e9486521-e667-4dc6-a663-57007d3c4944">
                <div class="modal-content e9486521-e667-4dc6-a663-57007d3c4944">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP63C3D58DF4E1380A388FBA630C99%2FP5YA0Z1W2K40V4YI.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=oRZplx377bX4msFCLiv7wunzHvI%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.e9486521-e667-4dc6-a663-57007d3c4944");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.e9486521-e667-4dc6-a663-57007d3c4944').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP63C3D58DF4E1380A388FBA630C99%2F92FQPGV96W0FC2K5.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=cPj32nal5mSfu1BVDFC6YxuNHuM%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_b4ac6450-ccaf-443b-a6c9-480fcdea032b" style="cursor: zoom-in;">
        <div id="img_b4ac6450-ccaf-443b-a6c9-480fcdea032b" class="modal fade b4ac6450-ccaf-443b-a6c9-480fcdea032b" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog b4ac6450-ccaf-443b-a6c9-480fcdea032b">
                <div class="modal-content b4ac6450-ccaf-443b-a6c9-480fcdea032b">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP63C3D58DF4E1380A388FBA630C99%2F92FQPGV96W0FC2K5.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=cPj32nal5mSfu1BVDFC6YxuNHuM%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.b4ac6450-ccaf-443b-a6c9-480fcdea032b");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.b4ac6450-ccaf-443b-a6c9-480fcdea032b').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_02</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1064" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 22:03:14</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC250803220308356d86f97';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC250803220308356d86f97')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 22:02:55</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1064&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1063" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1063</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>36573…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 3657361c572e40e8';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('3657361c572e40e8')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(3657361c572e40e8)" href="/admin/info/sub_orders?order_id=1063">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>79F35…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 79F35DF13F6DF1A01C667F9417343C09';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('79F35DF13F6DF1A01C667F9417343C09')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11178</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP2989756FCA0F222AF5BBAB99C04F%2FEQZKHHG2YI6U3N5F.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=w%2BndEKfJKPak%2FKdWQxQWIHYbf5U%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_68f91f07-d0a5-4f51-b139-e02636e5773e" style="cursor: zoom-in;">
        <div id="img_68f91f07-d0a5-4f51-b139-e02636e5773e" class="modal fade 68f91f07-d0a5-4f51-b139-e02636e5773e" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 68f91f07-d0a5-4f51-b139-e02636e5773e">
                <div class="modal-content 68f91f07-d0a5-4f51-b139-e02636e5773e">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP2989756FCA0F222AF5BBAB99C04F%2FEQZKHHG2YI6U3N5F.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=w%2BndEKfJKPak%2FKdWQxQWIHYbf5U%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.68f91f07-d0a5-4f51-b139-e02636e5773e");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.68f91f07-d0a5-4f51-b139-e02636e5773e').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP2989756FCA0F222AF5BBAB99C04F%2FL8SYGKZX79LEDJIU.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=uZGw70x0yP8r2f7XysmvTJzZ4eI%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_070135ec-2148-413f-9518-8a0ac3c75a8a" style="cursor: zoom-in;">
        <div id="img_070135ec-2148-413f-9518-8a0ac3c75a8a" class="modal fade 070135ec-2148-413f-9518-8a0ac3c75a8a" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 070135ec-2148-413f-9518-8a0ac3c75a8a">
                <div class="modal-content 070135ec-2148-413f-9518-8a0ac3c75a8a">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP2989756FCA0F222AF5BBAB99C04F%2FL8SYGKZX79LEDJIU.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=uZGw70x0yP8r2f7XysmvTJzZ4eI%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.070135ec-2148-413f-9518-8a0ac3c75a8a");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.070135ec-2148-413f-9518-8a0ac3c75a8a').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_02</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1063" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 21:08:49</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC250803210845383197f29';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC250803210845383197f29')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 21:08:32</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1063&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1062" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1062</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>7da4f…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 7da4fef49fe8467f';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('7da4fef49fe8467f')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(7da4fef49fe8467f)" href="/admin/info/sub_orders?order_id=1062">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>2585B…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 2585B60931895D32E3597F45ECB6FE98';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('2585B60931895D32E3597F45ECB6FE98')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11177</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPFE4EEC89B25A54AF260AA5FE7EA5%2FS5T9CYGWKNVHNXLA.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=yqdOdboLP5R9YKByAcCUvmHeRzg%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_10f95656-34a0-4770-97c8-48dd998c9d10" style="cursor: zoom-in;">
        <div id="img_10f95656-34a0-4770-97c8-48dd998c9d10" class="modal fade 10f95656-34a0-4770-97c8-48dd998c9d10" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 10f95656-34a0-4770-97c8-48dd998c9d10">
                <div class="modal-content 10f95656-34a0-4770-97c8-48dd998c9d10">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPFE4EEC89B25A54AF260AA5FE7EA5%2FS5T9CYGWKNVHNXLA.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=yqdOdboLP5R9YKByAcCUvmHeRzg%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.10f95656-34a0-4770-97c8-48dd998c9d10");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.10f95656-34a0-4770-97c8-48dd998c9d10').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPFE4EEC89B25A54AF260AA5FE7EA5%2F7AU3OJ46V6MX5OSG.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=A3hzW3Is%2BJEQmU%2Fzq9xYrz53mqU%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_34d84796-9568-467e-b500-d3c38cac3944" style="cursor: zoom-in;">
        <div id="img_34d84796-9568-467e-b500-d3c38cac3944" class="modal fade 34d84796-9568-467e-b500-d3c38cac3944" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 34d84796-9568-467e-b500-d3c38cac3944">
                <div class="modal-content 34d84796-9568-467e-b500-d3c38cac3944">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPFE4EEC89B25A54AF260AA5FE7EA5%2F7AU3OJ46V6MX5OSG.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=A3hzW3Is%2BJEQmU%2Fzq9xYrz53mqU%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.34d84796-9568-467e-b500-d3c38cac3944");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.34d84796-9568-467e-b500-d3c38cac3944').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1062" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:59:47</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC25080320594078408528c';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC25080320594078408528c')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:59:30</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1062&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1061" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1061</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>88b11…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 88b1172bfeff4737';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('88b1172bfeff4737')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(88b1172bfeff4737)" href="/admin/info/sub_orders?order_id=1061">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>F20F4…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: F20F4C348278C79B8BF17B1562A43387';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('F20F4C348278C79B8BF17B1562A43387')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11176</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP8737C084B184B9C40575DE855238%2FFREQQW78QVA52FOZ.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=DDCXZu3N30rsJZ62ARTSnc%2FPtTE%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_1874b0dd-9c3c-425f-bd2f-aa28eb61c723" style="cursor: zoom-in;">
        <div id="img_1874b0dd-9c3c-425f-bd2f-aa28eb61c723" class="modal fade 1874b0dd-9c3c-425f-bd2f-aa28eb61c723" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 1874b0dd-9c3c-425f-bd2f-aa28eb61c723">
                <div class="modal-content 1874b0dd-9c3c-425f-bd2f-aa28eb61c723">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP8737C084B184B9C40575DE855238%2FFREQQW78QVA52FOZ.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=DDCXZu3N30rsJZ62ARTSnc%2FPtTE%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.1874b0dd-9c3c-425f-bd2f-aa28eb61c723");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.1874b0dd-9c3c-425f-bd2f-aa28eb61c723').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP8737C084B184B9C40575DE855238%2FBDOSU9P3IYPAHOHT.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=fudj6POscp5tu2PZEhol%2BPUwgQU%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_df310e7a-f5ec-40d0-a17c-ef4ec11f9057" style="cursor: zoom-in;">
        <div id="img_df310e7a-f5ec-40d0-a17c-ef4ec11f9057" class="modal fade df310e7a-f5ec-40d0-a17c-ef4ec11f9057" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog df310e7a-f5ec-40d0-a17c-ef4ec11f9057">
                <div class="modal-content df310e7a-f5ec-40d0-a17c-ef4ec11f9057">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP8737C084B184B9C40575DE855238%2FBDOSU9P3IYPAHOHT.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=fudj6POscp5tu2PZEhol%2BPUwgQU%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.df310e7a-f5ec-40d0-a17c-ef4ec11f9057");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.df310e7a-f5ec-40d0-a17c-ef4ec11f9057').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1061" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:53:20</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC250803205310634fda7d4';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC250803205310634fda7d4')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:52:36</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1061&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1060" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1060</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>d3b36…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: d3b36d3d9dda466f';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('d3b36d3d9dda466f')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(d3b36d3d9dda466f)" href="/admin/info/sub_orders?order_id=1060">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#fff3e0;color:#f57c00;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #ffcc02;">
					<i class="fa fa-clock-o" style="margin-right:3px;"></i>待支付
				</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>476BF…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 476BFD89EE05B0FEB684A0F74BC20B58';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('476BFD89EE05B0FEB684A0F74BC20B58')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td><nil></nil></td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP85A0C3E6B8AFC20000A66C4045DF%2FDO3E2GQQXPA1GRQK.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=PsSexXF5xnK8YInj4fqJ0k03gtY%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_00121382-6ca3-4ce4-9fce-0def4269f4f5" style="cursor: zoom-in;">
        <div id="img_00121382-6ca3-4ce4-9fce-0def4269f4f5" class="modal fade 00121382-6ca3-4ce4-9fce-0def4269f4f5" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 00121382-6ca3-4ce4-9fce-0def4269f4f5">
                <div class="modal-content 00121382-6ca3-4ce4-9fce-0def4269f4f5">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP85A0C3E6B8AFC20000A66C4045DF%2FDO3E2GQQXPA1GRQK.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=PsSexXF5xnK8YInj4fqJ0k03gtY%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.00121382-6ca3-4ce4-9fce-0def4269f4f5");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.00121382-6ca3-4ce4-9fce-0def4269f4f5').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP85A0C3E6B8AFC20000A66C4045DF%2F4VZ7JXSQ2B8IKFER.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=vC46eEic0z4OQg4HDq5jbunDeFg%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_4007f6fe-fb1e-4750-9b1a-5ab5660b87ed" style="cursor: zoom-in;">
        <div id="img_4007f6fe-fb1e-4750-9b1a-5ab5660b87ed" class="modal fade 4007f6fe-fb1e-4750-9b1a-5ab5660b87ed" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 4007f6fe-fb1e-4750-9b1a-5ab5660b87ed">
                <div class="modal-content 4007f6fe-fb1e-4750-9b1a-5ab5660b87ed">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP85A0C3E6B8AFC20000A66C4045DF%2F4VZ7JXSQ2B8IKFER.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=vC46eEic0z4OQg4HDq5jbunDeFg%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.4007f6fe-fb1e-4750-9b1a-5ab5660b87ed");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.4007f6fe-fb1e-4750-9b1a-5ab5660b87ed').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1060" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#909399;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;"><circle cx="7" cy="7" r="6" fill="#909399"></circle></svg>未支付</span></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:45:55</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1060&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1059" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1059</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>3ac1b…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 3ac1b92e449c4eca';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('3ac1b92e449c4eca')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(3ac1b92e449c4eca)" href="/admin/info/sub_orders?order_id=1059">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>768F1…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 768F1B941ABD10211A056AAD3D675F0A';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('768F1B941ABD10211A056AAD3D675F0A')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11175</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPBF9ECEEE515A3F444607C14027A2%2FGMG6VIXJUCRFR6MR.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=NyBK0ZyjlMl8ru6VSPPrJQrr%2B70%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_8d5c431b-790c-4619-8e27-dc12379381a4" style="cursor: zoom-in;">
        <div id="img_8d5c431b-790c-4619-8e27-dc12379381a4" class="modal fade 8d5c431b-790c-4619-8e27-dc12379381a4" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 8d5c431b-790c-4619-8e27-dc12379381a4">
                <div class="modal-content 8d5c431b-790c-4619-8e27-dc12379381a4">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPBF9ECEEE515A3F444607C14027A2%2FGMG6VIXJUCRFR6MR.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=NyBK0ZyjlMl8ru6VSPPrJQrr%2B70%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.8d5c431b-790c-4619-8e27-dc12379381a4");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.8d5c431b-790c-4619-8e27-dc12379381a4').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPBF9ECEEE515A3F444607C14027A2%2F0RDLF3LX310USEBF.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=tLurDG3YyCJsMEgYf6qz6C6pRS8%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_867d948f-316d-4bf5-ab42-04d9a01a14dd" style="cursor: zoom-in;">
        <div id="img_867d948f-316d-4bf5-ab42-04d9a01a14dd" class="modal fade 867d948f-316d-4bf5-ab42-04d9a01a14dd" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 867d948f-316d-4bf5-ab42-04d9a01a14dd">
                <div class="modal-content 867d948f-316d-4bf5-ab42-04d9a01a14dd">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMPBF9ECEEE515A3F444607C14027A2%2F0RDLF3LX310USEBF.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=tLurDG3YyCJsMEgYf6qz6C6pRS8%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.867d948f-316d-4bf5-ab42-04d9a01a14dd");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.867d948f-316d-4bf5-ab42-04d9a01a14dd').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1059" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:13:33</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC250803201327395de4710';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC250803201327395de4710')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:13:16</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1059&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1058" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1058</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>838cb…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 838cb271c0cc41fe';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('838cb271c0cc41fe')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(838cb271c0cc41fe)" href="/admin/info/sub_orders?order_id=1058">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#e8f5e8;color:#2e7d32;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #c8e6c9;">
						<i class="fa fa-check-circle" style="margin-right:3px;"></i>成功
					</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>91B37…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 91B3774C9F4555E74BB3945D4AB4EA28';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('91B3774C9F4555E74BB3945D4AB4EA28')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>11174</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP4E29E13C2F31CAFBBA8FAD2E4D52%2FIV5BI3OKLXIRUNUT.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=cMBbgDZxzubk%2FI5M0jcavjPKnvg%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_d43d7223-f165-4c89-aa01-7a17d0012e3b" style="cursor: zoom-in;">
        <div id="img_d43d7223-f165-4c89-aa01-7a17d0012e3b" class="modal fade d43d7223-f165-4c89-aa01-7a17d0012e3b" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog d43d7223-f165-4c89-aa01-7a17d0012e3b">
                <div class="modal-content d43d7223-f165-4c89-aa01-7a17d0012e3b">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP4E29E13C2F31CAFBBA8FAD2E4D52%2FIV5BI3OKLXIRUNUT.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=cMBbgDZxzubk%2FI5M0jcavjPKnvg%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.d43d7223-f165-4c89-aa01-7a17d0012e3b");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.d43d7223-f165-4c89-aa01-7a17d0012e3b').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP4E29E13C2F31CAFBBA8FAD2E4D52%2FFRWZ9110XK7UT6SJ.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=j0UBOEEwIjXCzqGwEjuf2F5TIFQ%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_a82392df-072d-4eee-ae5e-a1b0f4d0164f" style="cursor: zoom-in;">
        <div id="img_a82392df-072d-4eee-ae5e-a1b0f4d0164f" class="modal fade a82392df-072d-4eee-ae5e-a1b0f4d0164f" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog a82392df-072d-4eee-ae5e-a1b0f4d0164f">
                <div class="modal-content a82392df-072d-4eee-ae5e-a1b0f4d0164f">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP4E29E13C2F31CAFBBA8FAD2E4D52%2FFRWZ9110XK7UT6SJ.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=j0UBOEEwIjXCzqGwEjuf2F5TIFQ%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.a82392df-072d-4eee-ae5e-a1b0f4d0164f");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.a82392df-072d-4eee-ae5e-a1b0f4d0164f').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1058" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#67C23A;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;" viewBox="0 0 1024 1024"><path d="M384 768L128 512l90.496-90.496L384 587.008l421.504-421.504L896 256z" fill="#67C23A"></path></svg>已支付</span></td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:12:27</td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>QC250…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: QC2508032012213801195b8';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('QC2508032012213801195b8')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 20:11:58</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1058&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1057" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1057</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>54eba…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 54eba9794d764e6f';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('54eba9794d764e6f')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(54eba9794d764e6f)" href="/admin/info/sub_orders?order_id=1057">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#fff3e0;color:#f57c00;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #ffcc02;">
					<i class="fa fa-clock-o" style="margin-right:3px;"></i>待支付
				</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>D14AA…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: D14AA312457137127EF94151C6C41722';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('D14AA312457137127EF94151C6C41722')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td><nil></nil></td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP75C880ADA2540FE3928AC9530193%2FELGHVXELL03I129Q.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=S2%2F0SxMn1ITGF2lGRI1F2gSHMSQ%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_1fd94865-d5ad-43eb-91c6-e2898e182758" style="cursor: zoom-in;">
        <div id="img_1fd94865-d5ad-43eb-91c6-e2898e182758" class="modal fade 1fd94865-d5ad-43eb-91c6-e2898e182758" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 1fd94865-d5ad-43eb-91c6-e2898e182758">
                <div class="modal-content 1fd94865-d5ad-43eb-91c6-e2898e182758">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP75C880ADA2540FE3928AC9530193%2FELGHVXELL03I129Q.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=S2%2F0SxMn1ITGF2lGRI1F2gSHMSQ%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.1fd94865-d5ad-43eb-91c6-e2898e182758");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.1fd94865-d5ad-43eb-91c6-e2898e182758').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP75C880ADA2540FE3928AC9530193%2FQFLDLBDIWS6FUW12.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=YJK%2B5psdRxOrux3usz7o%2B6vJ6vw%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_25fdd14e-473c-47d2-bc15-11ebc78da068" style="cursor: zoom-in;">
        <div id="img_25fdd14e-473c-47d2-bc15-11ebc78da068" class="modal fade 25fdd14e-473c-47d2-bc15-11ebc78da068" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 25fdd14e-473c-47d2-bc15-11ebc78da068">
                <div class="modal-content 25fdd14e-473c-47d2-bc15-11ebc78da068">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP75C880ADA2540FE3928AC9530193%2FQFLDLBDIWS6FUW12.png?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=YJK%2B5psdRxOrux3usz7o%2B6vJ6vw%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.25fdd14e-473c-47d2-bc15-11ebc78da068");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.25fdd14e-473c-47d2-bc15-11ebc78da068').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1057" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>59.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#909399;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;"><circle cx="7" cy="7" r="6" fill="#909399"></circle></svg>未支付</span></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 17:49:34</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1057&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
            <tr>
                
                    
                        <td style="text-align: center;">
                            <div class="icheckbox_minimal-blue" aria-checked="false" aria-disabled="false" style="position: relative;"><input type="checkbox" class="grid-row-checkbox" data-id="1056" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins></div>
                        </td>
                    
                
                
                    
                        
                            
                                <td>1056</td>
                            
                        
                    
                        
                            
                                <td><span style="display:inline-flex;align-items:center;">
		<span style="display:inline-flex;align-items:center;">
			<span>d6eb8…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: d6eb86701792441f';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('d6eb86701792441f')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    <span style="display:inline-block;width:12px;"></span>
    <a class=" new-tab-link" data-title="子订单列表(d6eb86701792441f)" href="/admin/info/sub_orders?order_id=1056">子订单</a>
</span><br><span style="display:inline-block;padding:3px 6px;background:#fff3e0;color:#f57c00;border-radius:3px;font-size:11px;font-weight:500;border:1px solid #ffcc02;">
					<i class="fa fa-clock-o" style="margin-right:3px;"></i>待支付
				</span></td>
                            
                        
                    
                        
                            
                                <td>
		<span style="display:inline-flex;align-items:center;">
			<span>87336…</span>
			<span style="margin-left:6px;cursor:pointer;color:#409EFF;" title="复制" onclick="(function(id){
				navigator.clipboard.writeText(id);
				var tip = document.createElement('div');
				tip.innerHTML = '已复制: 87336A4AE5A177CE214B72CF61818112';
				tip.style.position = 'fixed';
				tip.style.left = '50%';
				tip.style.top = '50%';
				tip.style.transform = 'translate(-50%, -50%)';
				tip.style.background = 'rgba(60,60,60,0.95)';
				tip.style.color = '#fff';
				tip.style.padding = '12px 28px';
				tip.style.borderRadius = '8px';
				tip.style.fontSize = '18px';
				tip.style.zIndex = 9999;
				tip.style.boxShadow = '0 2px 12px rgba(0,0,0,0.15)';
				document.body.appendChild(tip);
				setTimeout(function(){ document.body.removeChild(tip); }, 2000);
			})('87336A4AE5A177CE214B72CF61818112')">
				<svg t="1719040000000" class="icon" viewBox="0 0 1024 1024" width="16" height="16" style="vertical-align:middle;">
					<path d="M768 128H384c-35.2 0-64 28.8-64 64v64H192c-35.2 0-64 28.8-64 64v576c0 35.2 28.8 64 64 64h384c35.2 0 64-28.8 64-64v-64h128c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m-128 704c0 17.6-14.4 32-32 32H192c-17.6 0-32-14.4-32-32V256c0-17.6 14.4-32 32-32h128v480c0 35.2 28.8 64 64 64h256v64z m192-128c0 17.6-14.4 32-32 32H384c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h448c17.6 0 32 14.4 32 32v512z" fill="#409EFF"></path>
				</svg>
			</span>
		 </span>
    </td>
                            
                        
                    
                        
                            
                                <td><nil></nil></td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP538FA40F04350EA81EF00B49E48D%2FIA4JT9KZ98KFYYHB.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=%2B38m5WA4hvWwUj%2FdxCAh9vq0uWU%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_be63fb9a-772b-4584-9319-054f9381565e" style="cursor: zoom-in;">
        <div id="img_be63fb9a-772b-4584-9319-054f9381565e" class="modal fade be63fb9a-772b-4584-9319-054f9381565e" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog be63fb9a-772b-4584-9319-054f9381565e">
                <div class="modal-content be63fb9a-772b-4584-9319-054f9381565e">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP538FA40F04350EA81EF00B49E48D%2FIA4JT9KZ98KFYYHB.jpeg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=%2B38m5WA4hvWwUj%2FdxCAh9vq0uWU%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.be63fb9a-772b-4584-9319-054f9381565e");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.be63fb9a-772b-4584-9319-054f9381565e').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>
    
        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP538FA40F04350EA81EF00B49E48D%2FVO3ZOP7LM919GX7Q.jpg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=MGjb0T%2FnUPG20Krt3pz0syh%2Fivc%3D" width="100px" height="100px" data-toggle="modal" data-target="#img_797c471b-5212-4827-ac74-d01279ba62e2" style="cursor: zoom-in;">
        <div id="img_797c471b-5212-4827-ac74-d01279ba62e2" class="modal fade 797c471b-5212-4827-ac74-d01279ba62e2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog 797c471b-5212-4827-ac74-d01279ba62e2">
                <div class="modal-content 797c471b-5212-4827-ac74-d01279ba62e2">
                    <div class="modal-body">
                        <img src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/tmp%2F20250803%2FTEMP538FA40F04350EA81EF00B49E48D%2FVO3ZOP7LM919GX7Q.jpg?Expires=1754279674&amp;OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&amp;Signature=MGjb0T%2FnUPG20Krt3pz0syh%2Fivc%3D" class="img-responsive">
                    </div>
                </div>
            </div>
        </div>
        <script>
            function centerModal() {
                $(this).css('display', 'block');
                var $dialog = $(this).find(".modal-dialog.797c471b-5212-4827-ac74-d01279ba62e2");
                var offset = ($(window).height() - $dialog.height()) / 2;
                $dialog.css("margin-top", offset);
            }

            $('.modal.797c471b-5212-4827-ac74-d01279ba62e2').on('show.bs.modal', centerModal);
            $(window).on("resize", function () {
                $('.modal:visible').each(centerModal);
            });
        </script>
    
</td>
                            
                        
                    
                        
                            
                                <td>官方 / 自营 / 什刹海</td>
                            
                        
                    
                        
                            
                                <td>SCH_01</td>
                            
                        
                    
                        
                            
                                <td><div class="btn-group pull-" style="margin-right: 10px"><a class="info-btn-J6i8RVlCB8 btn btn-sm btn-default " data-toggle="modal" data-target="#info-popup-model-8waJmtZavO " data-id="1056" style="cursor: pointer;">
                    <i class="fa fa-repeat"></i>&nbsp;&nbsp;
                </a></div></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>-</td>
                            
                        
                    
                        
                            
                                <td>39.00</td>
                            
                        
                    
                        
                            
                                <td><span style="color:#909399;"><svg width="14" height="14" style="vertical-align:middle;margin-right:4px;"><circle cx="7" cy="7" r="6" fill="#909399"></circle></svg>未支付</span></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td></td>
                            
                        
                    
                        
                            
                                <td>大屏</td>
                            
                        
                    
                        
                            
                                <td>2025-08-03 17:16:05</td>
                            
                        
                    
                    
                        <td style="text-align: center;" class="last_th_td_ele">
                            
                                
                                
                                
                                    <a href="/admin/info/orders/detail?__page=1&amp;__pageSize=10&amp;__sort=id&amp;__sort_type=desc&amp;__goadmin_detail_pk=1056&amp;" class="grid-row-view">
                                        查看
                                    </a>
                                
                                
                            
                        </td>
                    
                
            </tr>
        
        </tbody>
    </table>
    
        <script>
            window.selectedRows = function () {
                let selected = [];
                let params = [];
                $('.grid-row-checkbox:checked').each(function () {
                    selected.push($(this).data('id'));
                    params.push($(this).data('param'));
                });
                return [selected, params];
            };

            const selectedAllFieldsRows = function () {
                let selected = [];
                $('.column-select-item:checked').each(function () {
                    selected.push($(this).data('id'));
                });
                return selected;
            };

            const pjaxContainer = "#pjax-container";
            const noAnimation = "__go_admin_no_animation_";

            function iCheck(el) {
                el.iCheck({checkboxClass: 'icheckbox_minimal-blue'}).on('ifChanged', function () {
                    if (this.checked) {
                        $(this).closest('tr').css('background-color', "#ffffd5");
                    } else {
                        $(this).closest('tr').css('background-color', '');
                    }
                });
            }

            $(function () {

                $('.grid-select-all').iCheck({checkboxClass: 'icheckbox_minimal-blue'}).on('ifChanged', function (event) {
                    if (this.checked) {
                        $('.grid-row-checkbox').iCheck('check');
                    } else {
                        $('.grid-row-checkbox').iCheck('uncheck');
                    }
                });
                let items = $('.column-select-item');
                iCheck(items);
                iCheck($('.grid-row-checkbox'));
                let columns = getQueryVariable("__columns");
                if (columns === -1) {
                    items.iCheck('check');
                } else {
                    let columnsArr = columns.split(",");
                    for (let i = 0; i < columnsArr.length; i++) {
                        for (let j = 0; j < items.length; j++) {
                            if (decodeURI(columnsArr[i]) === $(items[j]).attr("data-id")) {
                                $(items[j]).iCheck('check');
                            }
                        }
                    }
                }

                

                

                let lastTd = $("table tr:last td:last div");
                if (lastTd.hasClass("dropdown")) {
                    let popUpHeight = $("table tr:last td:last div ul").height();

                    let trs = $("table tr");
                    let totalHeight = 0;
                    for (let i = 1; i < trs.length - 1; i++) {
                        totalHeight += $(trs[i]).height();
                    }
                    if (popUpHeight > totalHeight) {
                        let h = popUpHeight + 16;
                        $("table tbody").append("<tr style='height:" + h + "px;'></tr>");
                    }

                    trs = $("table tr");
                    for (let i = trs.length - 1; i > 1; i--) {
                        let td = $(trs[i]).find("td:last-child div");
                        let combineHeight = $(trs[i]).height() / 2 - 20;
                        for (let j = i + 1; j < trs.length; j++) {
                            combineHeight += $(trs[j]).height();
                        }
                        if (combineHeight < popUpHeight) {
                            td.removeClass("dropdown");
                            td.addClass("dropup");
                        }
                    }
                }

                

                let sort = getQueryVariable("__sort");
                let sort_type = getQueryVariable("__sort_type");

                if (sort !== -1 && sort_type !== -1) {
                    let sortFa = $('#sort-' + sort);
                    if (sort_type === 'asc') {
                        sortFa.attr('href', '?__sort=' + sort + "&__sort_type=desc" + decodeURIComponent("\u0026__go_admin_no_animation_=true\u0026__pageSize=10"))
                    } else {
                        sortFa.attr('href', '?__sort=' + sort + "&__sort_type=asc" + decodeURIComponent("\u0026__go_admin_no_animation_=true\u0026__pageSize=10"))
                    }
                    sortFa.removeClass('fa-sort');
                    sortFa.addClass('fa-sort-amount-' + sort_type);
                } else {
                    let sortParam = decodeURIComponent("\u0026__go_admin_no_animation_=true\u0026__pageSize=10");
                    let sortHeads = $(".fa.fa-fw.fa-sort");
                    for (let i = 0; i < sortHeads.length; i++) {
                        $(sortHeads[i]).attr('href', $(sortHeads[i]).attr('href') + sortParam)
                    }
                }
            });

            
            
            

            

            $('.column-select-all').on('click', function () {
                if ($(this).data('check') === '') {
                    $('.column-select-item').iCheck('check');
                    $(this).data('check', 'true')
                } else {
                    $('.column-select-item').iCheck('uncheck');
                    $(this).data('check', '')
                }
            });

            $('.column-select-submit').on('click', function () {

                let param = new Map();
                param.set('__columns', selectedAllFieldsRows().join(','));
                param.set(noAnimation, 'true');

                $.pjax({
                    url: addParameterToURL(param),
                    container: pjaxContainer
                });

                toastr.success('加载成功 !');
            });

            

            
            
            

            
            
            

            

            $('.grid-batch-1').on('click', function () {
                let data = selectedRows();
                if (data[0].length > 0) {
                    ExportAll(data[0].join())
                }
            });

            function ExportAll(id) {
                let form = $("<form>");
                form.attr("style", "display:none");
                form.attr("target", "");
                form.attr("method", "post");
                form.attr("action","/admin/export/orders?__page=1\u0026__pageSize=10\u0026__sort=id\u0026__sort_type=desc");
                let input1 = $("<input>");
                input1.attr("type", "hidden");
                input1.attr("name","id");
                input1.attr("value", id);
                $("body").append(form);
                form.append(input1);
                form.submit();
                form.remove()
            }

            

            
            
            

            
            
            

            

            
            
            

            
            
            

            function getQueryVariable(variable) {
                let query = window.location.search.substring(1);
                let vars = query.split("&");
                for (let i = 0; i < vars.length; i++) {
                    let pair = vars[i].split("=");
                    if (pair[0] === variable) {
                        return pair[1];
                    }
                }
                return -1;
            }

            function addParameterToURL(params) {
                let newUrl = location.href.replace("#", "");

                for (let [field, value] of params) {
                    if (getQueryVariable(field) !== -1) {
                        newUrl = replaceParamVal(newUrl, field, value);
                    } else {
                        if (newUrl.indexOf("?") > 0) {
                            newUrl = newUrl + "&" + field + "=" + value;
                        } else {
                            newUrl = newUrl + "?" + field + "=" + value;
                        }
                    }
                }

                return newUrl
            }

            function replaceParamVal(oUrl, paramName, replaceWith) {
                let re = eval('/(' + paramName + '=)([^&]*)/gi');
                return oUrl.replace(re, paramName + '=' + replaceWith);
            }

            $(function () {

                $('.editable-td-select').editable({
                    "type": "select",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>"
                });
                $('.editable-td-text').editable({
                    emptytext: "<i class=\"fa fa-pencil\"><\/i>",
                    type: "text"
                });
                $('.editable-td-datetime').editable({
                    "type": "combodate",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>",
                    "format": "YYYY-MM-DD HH:mm:ss",
                    "viewformat": "YYYY-MM-DD HH:mm:ss",
                    "template": "YYYY-MM-DD HH:mm:ss",
                    "combodate": {"maxYear": 2035}
                });
                $('.editable-td-date').editable({
                    "type": "combodate",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>",
                    "format": "YYYY-MM-DD",
                    "viewformat": "YYYY-MM-DD",
                    "template": "YYYY-MM-DD",
                    "combodate": {"maxYear": 2035}
                });
                $('.editable-td-year').editable({
                    "type": "combodate",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>",
                    "format": "YYYY",
                    "viewformat": "YYYY",
                    "template": "YYYY",
                    "combodate": {"maxYear": 2035}
                });
                $('.editable-td-month').editable({
                    "type": "combodate",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>",
                    "format": "MM",
                    "viewformat": "MM",
                    "template": "MM",
                    "combodate": {"maxYear": 2035}
                });
                $('.editable-td-day').editable({
                    "type": "combodate",
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>",
                    "format": "DD",
                    "viewformat": "DD",
                    "template": "DD",
                    "combodate": {"maxYear": 2035}
                });
                $('.editable-td-textarea').editable({
                    "type": "textarea",
                    "rows": 10,
                    "emptytext": "<i class=\"fa fa-pencil\"><\/i>"
                });
                $(".info_edit_switch").bootstrapSwitch({
                    onSwitchChange: function (event, state) {
                        let obejct = $(event.target);
                        let val = "";
                        if (state) {
                            val = obejct.closest('.bootstrap-switch').next().val();
                        } else {
                            val = obejct.closest('.bootstrap-switch').next().next().val()
                        }
                        $.ajax({
                            method: 'post',
                            url: obejct.data("updateurl"),
                            data: {
                                name: obejct.data("field"),
                                value: val,
                                pk: obejct.data("pk")
                            },
                            success: function (data) {
                                if (typeof (data) === "string") {
                                    data = JSON.parse(data);
                                }
                                if (data.code !== 200) {
                                    swal(data.msg, '', 'error');
                                }
                            },
                            error: function (data) {
                                if (data.responseText !== "") {
                                    swal(data.responseJSON.msg, '', 'error');
                                } else {
                                    swal("错误", '', 'error');
                                }
                            },
                        });
                    }
                })
            });

            
        </script>
        <style>
            table tbody tr td {
                word-wrap: break-word;
                word-break: break-all;
            }
            table.sticky_table tbody th:last-child, table.sticky_table tbody td:last-child {
                position: sticky;
                right: 0;
                z-index: 1;
            }
            table tbody td:last-child {
                background-color: white;
            }
            table.sticky_table .last_th_td_ele:before, table.sticky_table .last_th_td_ele:before {
                left: 10px;
                box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, .15);
                content: "";
                position: absolute;
                top: 0;
                width: 10px;
                bottom: -1px;
                overflow-x: hidden;
                overflow-y: hidden;
                touch-action: none;
                pointer-events: none;
            }            
        </style>
    

    </div>